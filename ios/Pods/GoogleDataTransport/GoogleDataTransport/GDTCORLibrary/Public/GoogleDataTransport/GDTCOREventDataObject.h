/*
 * Copyright 2018 Google
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/** This protocol defines the common interface that event protos should implement regardless of the
 * underlying transport technology (protobuf, nanopb, etc).
 */
@protocol GDTCOREventDataObject <NSObject>

@required

/** Returns the serialized proto bytes of the implementing event proto.
 *
 * @return the serialized proto bytes of the implementing event proto.
 */
- (NSData *)transportBytes;

@end

NS_ASSUME_NONNULL_END
