// Copyright 2022 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#import <Foundation/Foundation.h>

#import "GoogleDataTransport/GDTCORLibrary/Private/GDTCORStorageMetadata.h"

@implementation GDTCORStorageMetadata

- (instancetype)initWithCurrentCacheSize:(GDTCORStorageSizeBytes)currentCacheSize
                            maxCacheSize:(GDTCORStorageSizeBytes)maxCacheSize {
  self = [super init];
  if (self) {
    _currentCacheSize = currentCacheSize;
    _maxCacheSize = maxCacheSize;
  }
  return self;
}

+ (instancetype)metadataWithCurrentCacheSize:(GDTCORStorageSizeBytes)currentCacheSize
                                maxCacheSize:(GDTCORStorageSizeBytes)maxCacheSize {
  return [[self alloc] initWithCurrentCacheSize:currentCacheSize maxCacheSize:maxCacheSize];
}

@end
