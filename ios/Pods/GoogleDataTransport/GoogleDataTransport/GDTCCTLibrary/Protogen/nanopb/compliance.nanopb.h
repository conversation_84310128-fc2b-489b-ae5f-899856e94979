/*
 * Copyright 2023 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* Automatically generated nanopb header */
/* Generated by nanopb-0.3.9.9 */

#ifndef PB_GDT_CCT_COMPLIANCE_NANOPB_H_INCLUDED
#define PB_GDT_CCT_COMPLIANCE_NANOPB_H_INCLUDED
#include <nanopb/pb.h>

#include "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/external_privacy_context.nanopb.h"

/* @@protoc_insertion_point(includes) */
#if PB_PROTO_HEADER_VERSION != 30
#error Regenerate this file with the current version of nanopb generator.
#endif


/* Enum definitions */
typedef enum _gdt_cct_ComplianceData_ProductIdOrigin {
    gdt_cct_ComplianceData_ProductIdOrigin_NOT_SET = 0,
    gdt_cct_ComplianceData_ProductIdOrigin_EVENT_OVERRIDE = 5
} gdt_cct_ComplianceData_ProductIdOrigin;
#define _gdt_cct_ComplianceData_ProductIdOrigin_MIN gdt_cct_ComplianceData_ProductIdOrigin_NOT_SET
#define _gdt_cct_ComplianceData_ProductIdOrigin_MAX gdt_cct_ComplianceData_ProductIdOrigin_EVENT_OVERRIDE
#define _gdt_cct_ComplianceData_ProductIdOrigin_ARRAYSIZE ((gdt_cct_ComplianceData_ProductIdOrigin)(gdt_cct_ComplianceData_ProductIdOrigin_EVENT_OVERRIDE+1))

/* Struct definitions */
typedef struct _gdt_cct_ComplianceData {
    bool has_privacy_context;
    privacy_context_external_ExternalPrivacyContext privacy_context;
    bool has_product_id_origin;
    gdt_cct_ComplianceData_ProductIdOrigin product_id_origin;
/* @@protoc_insertion_point(struct:gdt_cct_ComplianceData) */
} gdt_cct_ComplianceData;

/* Default values for struct fields */
extern const gdt_cct_ComplianceData_ProductIdOrigin gdt_cct_ComplianceData_product_id_origin_default;

/* Initializer values for message structs */
#define gdt_cct_ComplianceData_init_default      {false, privacy_context_external_ExternalPrivacyContext_init_default, false, gdt_cct_ComplianceData_ProductIdOrigin_NOT_SET}
#define gdt_cct_ComplianceData_init_zero         {false, privacy_context_external_ExternalPrivacyContext_init_zero, false, _gdt_cct_ComplianceData_ProductIdOrigin_MIN}

/* Field tags (for use in manual encoding/decoding) */
#define gdt_cct_ComplianceData_privacy_context_tag 1
#define gdt_cct_ComplianceData_product_id_origin_tag 2

/* Struct field encoding specification for nanopb */
extern const pb_field_t gdt_cct_ComplianceData_fields[3];

/* Maximum encoded size of messages (where known) */
#define gdt_cct_ComplianceData_size              (14 + privacy_context_external_ExternalPRequestContext_size)

/* Message IDs (where set with "msgid" option) */
#ifdef PB_MSGID

#define COMPLIANCE_MESSAGES \


#endif

/* @@protoc_insertion_point(eof) */

#endif
